EXE_INC = \
    -I../turbulenceModels/lnInclude \
    -I$(LIB_SRC)/transportModels/compressible/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/basic/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/specie/lnInclude \
    -I$(LIB_FGM_SRC)/thermophysicalModels/solidThermo/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/solidSpecie/lnInclude \
    -I$(LIB_SRC)/finiteVolume/lnInclude \
    -I$(LIB_SRC)/meshTools/lnInclude \

LIB_LIBS = \
    -L$(FOAM_USER_LIBBIN) \
    -lcompressibleTransportModels \
    -lfluidThermophysicalModels \
    -lFGMFoamSolidThermo \
    -lsolidSpecie \
    -lFGMFoamTurbulenceModels \
    -lspecie \
    -lfiniteVolume \
    -lmeshTools
