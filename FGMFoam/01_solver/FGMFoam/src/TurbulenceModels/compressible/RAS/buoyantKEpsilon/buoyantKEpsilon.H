/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     | Website:  https://openfoam.org
    \\  /    A nd           | Copyright (C) 2014-2019 OpenFOAM Foundation
     \\/     M anipulation  |
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

Class
    Foam::RASModels::buoyantKEpsilon

Description
    Additional buoyancy generation/dissipation term applied to the
    k and epsilon equations of the standard k-epsilon model.

    Reference:
    \verbatim
        Henkes, R.A.W.M., Van Der Vlugt, F.F. & Hoogendoorn, C.J. (1991).
        Natural Convection Flow in a Square Cavity Calculated with
        Low-Reynolds-Number Turbulence Models.
        Int. J. Heat Mass Transfer, 34, 1543-1557.
    \endverbatim

    This implementation is based on the density rather than temperature gradient
    extending the applicability to systems in which the density gradient may be
    generated by variation of composition rather than temperature.  Further, the
    1/Prt coefficient is replaced by Cg to provide more general control of
    model.

    The default model coefficients are
    \verbatim
        buoyantKEpsilonCoeffs
        {
            Cg              1.0;
        }
    \endverbatim

See also
    Foam::RASModels::kEpsilon

SourceFiles
    buoyantKEpsilon.C

\*---------------------------------------------------------------------------*/

#ifndef buoyantKEpsilon_H
#define buoyantKEpsilon_H

#include "kEpsilon.H"

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

namespace Foam
{
namespace RASModels
{

/*---------------------------------------------------------------------------*\
                           Class buoyantKEpsilon Declaration
\*---------------------------------------------------------------------------*/

template<class BasicTurbulenceModel>
class buoyantKEpsilon
:
    public kEpsilon<BasicTurbulenceModel>
{
protected:

    // Protected data

        // Model coefficients

            dimensionedScalar Cg_;


    // Protected Member Functions

        tmp<volScalarField> Gcoef() const;

        virtual tmp<fvScalarMatrix> kSource() const;
        virtual tmp<fvScalarMatrix> epsilonSource() const;


public:

    typedef typename BasicTurbulenceModel::alphaField alphaField;
    typedef typename BasicTurbulenceModel::rhoField rhoField;
    typedef typename BasicTurbulenceModel::transportModel transportModel;


    //- Runtime type information
    TypeName("buoyantKEpsilon");


    // Constructors

        //- Construct from components
        buoyantKEpsilon
        (
            const alphaField& alpha,
            const rhoField& rho,
            const volVectorField& U,
            const surfaceScalarField& alphaRhoPhi,
            const surfaceScalarField& phi,
            const transportModel& transport,
            const word& propertiesName = turbulenceModel::propertiesName,
            const word& type = typeName
        );

        //- Disallow default bitwise copy construction
        buoyantKEpsilon(const buoyantKEpsilon&) = delete;


    //- Destructor
    virtual ~buoyantKEpsilon()
    {}


    // Member Functions

        //- Re-read model coefficients if they have changed
        virtual bool read();


    // Member Operators

        //- Disallow default bitwise assignment
        void operator=(const buoyantKEpsilon&) = delete;
};


// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

} // End namespace RASModels
} // End namespace Foam

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

#ifdef NoRepository
    #include "buoyantKEpsilon.C"
#endif

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

#endif

// ************************************************************************* //
