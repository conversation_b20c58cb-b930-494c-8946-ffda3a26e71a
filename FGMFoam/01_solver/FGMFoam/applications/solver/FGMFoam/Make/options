EXE_INC = \
    -I$(LIB_SRC)/transportModels/compressible/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/basic/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/specie/lnInclude \
    -I$(LIB_FGM_SRC)/thermophysicalModels/reactionThermo/lnInclude \
    -I$(LIB_FGM_SRC)/thermophysicalModels/chemistryModel/lnInclude \
    -I$(LIB_FGM_SRC)/fvOptions/lnInclude \
    -I$(LIB_SRC)/ODE/lnInclude \
    -I$(LIB_SRC)/finiteVolume/lnInclude \
    -I$(LIB_SRC)/meshTools/lnInclude \
    -I$(LIB_SRC)/sampling/lnInclude \
    -I$(LIB_FGM_SRC)/TurbulenceModels/turbulenceModels/lnInclude \
    -I$(LIB_FGM_SRC)/TurbulenceModels/compressible/lnInclude \
    -I$(LIB_FGM_SRC)/combustionModels/lnInclude 


EXE_LIBS = \
    -L$(FOAM_USER_LIBBIN) \
    -lcompressibleTransportModels \
    -lfluidThermophysicalModels \
    -lspecie \
    -lFGMFoamReactionThermophysicalModels \
    -lFGMFoamChemistryModel \
    -lFGMFoamFvOptions \
    -lODE \
    -lfiniteVolume \
    -lmeshTools \
    -lsampling \
    -lFGMFoamTurbulenceModels \
    -lFGMFoamCompressibleTurbulenceModels \
    -lFGMFoamCombustionModels
    
