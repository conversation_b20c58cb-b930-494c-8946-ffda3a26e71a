EXE_INC = \
    -I$(LIB_SRC)/OpenFOAM/lnInclude \
    -I$(LIB_SRC)/meshTools/lnInclude \
    -I$(LIB_SRC)/transportModels/compressible/lnInclude \
    -I$(LIB_SRC)/finiteVolume/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/basic/lnInclude \
    -I$(LIB_SRC)/thermophysicalModels/specie/lnInclude \
    -I$(LIB_FGM_SRC)/thermophysicalModels/reactionThermo/lnInclude \
    -I$(LIB_FGM_SRC)/thermophysicalModels/chemistryModel/lnInclude \
    -I$(LIB_FGM_SRC)/TurbulenceModels/turbulenceModels/lnInclude \
    -I$(LIB_FGM_SRC)/TurbulenceModels/compressible/lnInclude \
    -I$(LIB_FGM_SRC)/combustionModels/lnInclude
    
EXE_LIBS = \
    -L$(FOAM_USER_LIBBIN) \
    -lfiniteVolume \
    -lcompressibleTransportModels \
    -lODE \
    -lfluidThermophysicalModels \
    -lspecie \
    -lFGMFoamReactionThermophysicalModels \
    -lFGMFoamChemistryModel \
    -lFGMFoamTurbulenceModels \
    -lFGMFoamCompressibleTurbulenceModels \
    -lFGMFoamCombustionModels \
    -lsampling
    
